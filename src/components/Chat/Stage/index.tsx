import styled from '@emotion/styled';
import {ErrorBoundary} from 'react-error-boundary';
import {useEffect} from 'react';
import {useCurrentChat, useStage} from '@/regions/staff/chat';
import {colors} from '@/constants/colors';
import {apiPostReport} from '@/api/staff';
import {DEFAULT_TASK_ID} from '@/constants/staff';
import {MessageProvider} from '../Provider/MessageProvider';
import {AlertFallbackPage} from './AlertFallbackPage';
import {StageActions} from './StageActions';
import {StageIframePanel} from './StageIframePanel';
import {StageStepPanel} from './StageStepPanel';
import {StageDiffPanel} from './StageDiffPanel';
import {StageTextPanel} from './StageTextPanel';
import {StageTabPanel} from './StageTabPanel';
import {StageF2CPanel} from './StageF2CPanel/index';
import {StageCollapsePanel} from './StageCollapsePanel';
import {StageIapiPanel} from './StageIapiPanel';

const stageComponentMap: Record<string, React.FC<{ stage: any }>> = {
    text: StageTextPanel,
    iframe: StageIframePanel,
    steps: StageStepPanel,
    diff: StageDiffPanel,
    f2c: StageF2CPanel,
    tabs: StageTabPanel,
    collapses: StageCollapsePanel,
    iapi: StageIapiPanel,
};

const StageContent = () => {
    const {currentStageId, conversationId, agentId, currentTaskId} = useCurrentChat();
    const stage = useStage(currentTaskId ?? DEFAULT_TASK_ID, currentStageId ?? '结果预览');

    useEffect(
        () => {
            if (currentStageId === '结果预览') {
                apiPostReport({
                    agentId,
                    metricName: 'taskResultView',
                    metricValue: 1,
                    labels: [
                        {
                            key: 'conversationId',
                            value: conversationId || '',
                        },
                    ],
                });
            }
        },
        [agentId, currentStageId, conversationId]
    );

    if (!stage) {
        return null;
    }

    const Component = stageComponentMap[stage.type];
    if (!Component) {
        return null;
    }

    if (stage.type === 'iapi') {
        return <Component stage={stage} key={stage.taskId + stage.id} />;
    }

    return <Component stage={stage} />;
};

const Split = styled.div`
    height: 1px;
    background-color: ${colors['gray-5']};
`;

export const Stage = () => {
    const {agentId, currentTaskId, currentStageId} = useCurrentChat();
    const stage = useStage(currentTaskId ?? DEFAULT_TASK_ID, currentStageId ?? '结果预览');
    if (!stage) {
        return null;
    }
    const {actions, messageId} = stage;
    return (
        <MessageProvider messageId={messageId} agentId={agentId}>
            <ErrorBoundary
                key={`${currentStageId ?? '结果预览'}`}
                FallbackComponent={AlertFallbackPage}
            >
                <StageContent />
            </ErrorBoundary>
            {Boolean(actions?.length) && (
                <>
                    <Split />
                    <StageActions actions={actions} />
                </>
            )}
        </MessageProvider>
    );
};
