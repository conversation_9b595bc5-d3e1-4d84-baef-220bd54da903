import {createMappedRegion} from 'region-react';
import {Chat, ChatMessage, MessageTask} from '@/types/staff/chat';
import {ChatStage} from '@/types/staff/stage';
import {apiGetChatById} from '@/api/staff';
import {DEFAULT_TASK_ID} from '@/constants/staff';
import {useCurrentChatId, getCurrentChatId} from './chatSdk';

// 每一个 Chat 对应一个 sse，sse message 到达的时候，把 messageId 存 chatRegion 里，然后把 message 存 messageRegion 里
// 这样的好处是，如果 messageId 没变，那么 chatRegion 就不需要操作，只操作 message，在页面上，就只有当前的 Message 在渲染，而其他的地方不渲染
const chatRegion = createMappedRegion<string, Chat>({
    conversationId: '',
    agentId: 0,
    createTime: new Date().toLocaleDateString(),
    taskIds: [],
    stageIds: {},
    currentStageId: '结果预览',
    currentTaskId: DEFAULT_TASK_ID,
    delegationType: 'AUTO',
});

export const getCurrentChat = () => {
    const currentChatId = getCurrentChatId();
    return chatRegion.getValue(currentChatId);
};

export const useCurrentChat = () => {
    const currentChatId = useCurrentChatId();
    return chatRegion.useValue(currentChatId);
};

// 注意如果这里 conversationId 需要变，就直接改 conversationId 触发所有的初始化
export const setChat = chatRegion.set;

export const loadChatData = chatRegion.loadBy(
    params => params.conversationId,
    apiGetChatById
);

const messageRegion = createMappedRegion<string, ChatMessage>();

export const useMessage = messageRegion.useValue;

export const setMessage = messageRegion.set;

const stageRegion = createMappedRegion<string, ChatStage>();

export const useStage = (taskId: string, stageId: string) => stageRegion.useValue(`${taskId}.${stageId}`);

export const getStage = (taskId: string, stageId: string) => stageRegion.getValue(`${taskId}.${stageId}`);

export const setStage =
    (taskId: string, stageId: string, value: ChatStage) => stageRegion.set(`${taskId}.${stageId}`, value);

export const resetAllStage = stageRegion.resetAll;


const taskRegion = createMappedRegion<string, MessageTask>();

export const useTask = taskRegion.useValue;

export const getTask = taskRegion.getValue;

export const setTask = taskRegion.set;

export const resetAllTask = taskRegion.resetAll;
