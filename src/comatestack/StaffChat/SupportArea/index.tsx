import styled from '@emotion/styled';
import {Flex, Tabs, Tooltip} from 'antd';
import {useCallback, useMemo} from 'react';
import {setChat, useCurrentChat} from '@/regions/staff/chat';
import {Stage} from '@/components/Chat/Stage';
import {colors} from '@/constants/colors';
import {staffColors} from '@/constants/colors/staff';
import {ConversationIdProvider} from '@/components/Chat/Provider/ConversationIdProvider';
import {DEFAULT_TASK_ID} from '@/constants/staff';
import {HeaderLeft} from './HeaderLeft';
import {HeaderRight} from './HeaderRight';

const Container = styled.div`
    height: 100vh;
    display: flex;
    flex-direction: column;
`;

const Header = styled(Flex)`
    z-index: 2;
    background: ${colors.white};
    align-items: center;
    justify-content: space-between;
    height: 52px;
`;

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-nav-wrap {
        height: 52px;
        margin-left: 20px;
        .ant-5-tabs-tab {
            height: 52px;
            padding: 20px 0;
        }
        .ant-5-tabs-tab.ant-5-tabs-tab-active .ant-5-tabs-tab-btn {
            color: transparent;
            background: ${staffColors.primaryBg};
            background-clip: text;
        }
    }
    .ant-5-tabs-tab-btn {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #545454;
    }
    .ant-5-tabs-ink-bar {
        background: ${staffColors.primaryBg} !important;
    }

    background: ${colors.white};
`;

const TabItems = [{key: '概览', label: '概览'}, {key: '运行过程', label: '运行过程'}, {key: '结果预览', label: '结果预览'}];

export const SupportArea = () => {
    const {conversationId, agentId, currentStageId, currentTaskId, stageIds} = useCurrentChat();
    const stringifiedStageIds = JSON.stringify(stageIds);
    const tabItems = useMemo(
        () => {
            try {
                const stageIds = JSON.parse(stringifiedStageIds);
                return TabItems.map(item => {
                    if (stageIds?.[currentTaskId ?? DEFAULT_TASK_ID]?.includes(item.key)) {
                        return item;
                    }
                    return {
                        ...item,
                        label: (
                            <Tooltip title="任务还未完成，暂时无法查看">
                                <span>{item.label}</span>
                            </Tooltip>
                        ),
                        disabled: true,
                    };
                });
            } catch (e) {
                return TabItems;
            }
        },
        [currentTaskId, stringifiedStageIds]
    );

    const handleChange = useCallback(
        (key: string) => {
            setChat(conversationId, item => ({
                ...item,
                currentStageId: key,
            }));
        },
        [conversationId]
    );

    const operations = {
        left: <HeaderLeft />,
        right: <HeaderRight />,
    };

    if (agentId === 1 || agentId === 2) {
        return (
            <ConversationIdProvider conversationId={conversationId}>
                <Container>
                    <Header>
                        <HeaderLeft />
                        <HeaderRight />
                    </Header>
                    <Stage />
                </Container>
            </ConversationIdProvider>
        );
    }

    return (
        <ConversationIdProvider conversationId={conversationId}>
            <Container>
                <Header>
                    <StyledTabs
                        tabBarExtraContent={operations}
                        activeKey={currentStageId}
                        onChange={handleChange}
                        items={tabItems}
                    />
                </Header>
                <Stage />
            </Container>
        </ConversationIdProvider>
    );
};
